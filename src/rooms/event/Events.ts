//客户端消息
import {Player} from "../schema/BuzUnoRoomState";

export enum ClientMsgType {
    //抽牌
    DRAW_CARD_TYPE = "DRAW_CARD",
    //出牌 ,需要配合PokerMeta 传入给服务端. 如果是万能卡需要额外自己赋值颜色传入
    PLAY_CARD_TYPE = "PLAY_CARD",
    //按下 UNO 按钮
    UNO_BUTTON_PRESSED_TYPE = "UNO_BUTTON_PRESSED",
    //准备状态变化
    TOGGLE_READY_TYPE = "TOGGLE_READY",
    //切换观众视角 ,需要额外透传需要参考的观众 { buzWatchID:xxxxxx}
    VISITOR_TYPE = "VISITOR"
}

//VISITOR_TYPE使用
export class VisitorMsg {
    buzWatchID: string;
}

export class VICTOR_INFOMsg {
    player: Player;
}

//服务端消息
export enum ServerMsgType {
    //游戏胜利后展示的弹窗 { victor:player  }
    VICTOR_INFO = "VICTOR_INFO",

}

