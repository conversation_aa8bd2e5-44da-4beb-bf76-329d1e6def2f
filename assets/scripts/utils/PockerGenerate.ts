import { PokerMeta, PokerType } from "../network/schema/Poker";


export class PockerGenerate {

    /**
        * 根据卡片类型、颜色和值生成资源名
        */
    public static generateResourceName(pokerMeta: PokerMeta): string {
        // 卡牌背景
        if (pokerMeta.type === PokerType.Back) {
            return 'bg';
        }

        // 万能卡
        if (pokerMeta.type === PokerType.Wild) {
            return 'w_1';
        }
        if (pokerMeta.type === PokerType.WildDrawFour) {
            return 'w_4';
        }

        // 颜色卡片 - 根据规则文件映射
        const colorMap = ['r', 'g', 'b', 'y']; // Red=0->r, Green=1->g, Blue=2->b, Yellow=3->y
        const colorPrefix = colorMap[pokerMeta.color];

        if (!colorPrefix) {
            return 'bg'; // 默认返回背景
        }

        let valueStr = '';
        switch (pokerMeta.type) {
            case PokerType.Number:
                // 数字卡：0-9
                valueStr = pokerMeta.value.toString();
                break;
            case PokerType.Skip:
                // 跳过卡：10
                valueStr = '10';
                break;
            case PokerType.Reverse:
                // 反转卡：11
                valueStr = '11';
                break;
            case PokerType.DrawTwo:
                // +2卡：12
                valueStr = '12';
                break;
            default:
                valueStr = '0';
        }

        return `${colorPrefix}_${valueStr}`;
    }
}