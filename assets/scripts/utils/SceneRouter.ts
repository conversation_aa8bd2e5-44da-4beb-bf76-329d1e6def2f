import { _decorator, Component, director, Node } from 'cc';
import { UnoRoomState } from '../network/schema/BuzUnoRoomState';


export class SceneRouter {
      // 👇 单例核心
    private static _instance: SceneRouter;
    public static get instance(): SceneRouter {
        if (!SceneRouter._instance) {
            SceneRouter._instance = new SceneRouter();
        }
        return SceneRouter._instance;
    }
    private constructor() { 
        console.log('SceneRouter created');
    }

    static readonly SCENE_WAITING = "WaitingRoomScene"
    static readonly SCENE_GAME = "GameScene"

    public async gotoScene(sceneName: number) {
        
        if (UnoRoomState.SCENE_WAITING === sceneName && director.getScene().name != SceneRouter.SCENE_WAITING) {
            director.loadScene(SceneRouter.SCENE_WAITING)
            return
        }
        
        if (UnoRoomState.SCENE_GAME === sceneName && director.getScene().name != SceneRouter.SCENE_GAME) {
            director.loadScene(SceneRouter.SCENE_GAME)
            return
        }
        
    }
   
}

