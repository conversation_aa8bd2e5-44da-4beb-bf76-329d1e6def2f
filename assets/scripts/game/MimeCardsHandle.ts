import { _decorator, Component, Node, Prefab, instantiate } from 'cc';
import { NetworkManager } from '../network/NetworkManager';
import { UnoRoomState, UserCards } from '../network/schema/BuzUnoRoomState';
import { PokerMeta } from '../network/schema/Poker';
import { PokerComponent } from '../bean/PokerComponent';
const { ccclass, property } = _decorator;

@ccclass('MimeCardsHandle')
export class MimeCardsHandle extends Component {

    @property({type: Node})
    container: Node = null;

    @property({type: Prefab})
    cardPrefab: Prefab = null;

    // 网络管理器实例
    private networkManager: NetworkManager;

    // 当前玩家手牌节点数组，用于管理卡牌实例
    private cardNodes: Node[] = [];

    onEnable() {
        console.log("GameMimeCards onEnable - 注册网络监听");
        // 获取网络管理器实例
        this.networkManager = NetworkManager.instance;

        // 注册房间状态变化监听
        this.networkManager.on('roomStateChanged', this.onRoomStateChanged, this);

        // 首次获取当前房间状态
        this.initializeRoomState();
    }

    onDisable() {
        console.log("GameMimeCards onDisable - 销毁网络监听");
        if (this.networkManager) {
            // 销毁房间状态变化监听
            this.networkManager.off('roomStateChanged', this.onRoomStateChanged, this);
        }
    }

    start() {

    }

    update(deltaTime: number) {

    }

    /**
     * 初始化房间状态 - 获取当前房间状态并处理手牌数据
     */
    private initializeRoomState() {
        const currentState = this.networkManager.getCurrentRoomState();
        if (currentState) {
            console.log("GameMimeCards: 初始化房间状态", currentState);
            this.onRoomStateChanged(currentState);
        } else {
            console.log("GameMimeCards: 当前没有房间状态数据");
        }
    }

    /**
     * 房间状态变化回调
     * @param roomState 新的房间状态
     */
    private onRoomStateChanged(roomState: UnoRoomState) {
        console.log("GameMimeCards: 房间状态已更新", roomState);

        // 处理当前玩家手牌显示
        this.updatePlayerCards(roomState);
    }

    /**
     * 更新当前玩家手牌显示
     * @param roomState 房间状态
     */
    private updatePlayerCards(roomState: UnoRoomState) {
        try {
            // 1. 找到当前玩家
            const currentPlayer = roomState.userList.find(player => player.isMe);
            if (!currentPlayer) {
                console.log("GameMimeCards: 未找到当前玩家");
                return;
            }

            console.log(`GameMimeCards: 找到当前玩家 ${currentPlayer.name} (${currentPlayer.uid})`);

            // 2. 从 buzUnoCards.usersCardList 中获取当前玩家的手牌
            const currentPlayerCards = roomState.buzUnoCards.usersCardList.find(
                userCards => userCards.uid === currentPlayer.uid
            );

            if (!currentPlayerCards) {
                console.log("GameMimeCards: 未找到当前玩家的手牌数据");
                this.clearAllCards();
                return;
            }

            console.log(`GameMimeCards: 找到当前玩家手牌，共 ${currentPlayerCards.cards.length} 张`);

            // 3. 清理旧的卡牌实例
            this.clearAllCards();

            // 4. 为每张手牌创建卡牌实例
            this.createCardInstances(currentPlayerCards.cards);

        } catch (error) {
            console.error("GameMimeCards: 更新玩家手牌时发生错误", error);
        }
    }

    /**
     * 清理所有卡牌实例
     */
    private clearAllCards() {
        // 销毁所有现有的卡牌节点
        this.cardNodes.forEach(cardNode => {
            if (cardNode && cardNode.isValid) {
                cardNode.destroy();
            }
        });

        // 清空数组
        this.cardNodes = [];

        console.log("GameMimeCards: 已清理所有卡牌实例");
    }

    /**
     * 创建卡牌实例
     * @param cards 卡牌数据数组
     */
    private createCardInstances(cards: PokerMeta[]) {
        if (!this.cardPrefab) {
            console.error("GameMimeCards: cardPrefab 未设置");
            return;
        }

        if (!this.container) {
            console.error("GameMimeCards: container 未设置");
            return;
        }

        console.log(`GameMimeCards: 开始创建 ${cards.length} 张卡牌实例`);

        cards.forEach((pokerMeta, index) => {
            try {
                // 实例化卡牌预制体
                const cardNode = instantiate(this.cardPrefab);

                // 获取 PokerComponent 组件
                const pokerComponent = cardNode.getComponent(PokerComponent);
                if (pokerComponent) {
                    // 设置卡牌数据
                    pokerComponent.setPockerMeta(pokerMeta);
                    console.log(`GameMimeCards: 设置卡牌 ${index} 数据:`, pokerMeta);
                } else {
                    console.error(`GameMimeCards: 卡牌预制体中未找到 PokerComponent 组件`);
                }

                // 添加到容器
                this.container.addChild(cardNode);

                // 保存节点引用
                this.cardNodes.push(cardNode);

                console.log(`GameMimeCards: 成功创建卡牌 ${index}`);

            } catch (error) {
                console.error(`GameMimeCards: 创建卡牌 ${index} 时发生错误`, error);
            }
        });

        console.log(`GameMimeCards: 完成创建卡牌实例，共 ${this.cardNodes.length} 张`);
    }

}

