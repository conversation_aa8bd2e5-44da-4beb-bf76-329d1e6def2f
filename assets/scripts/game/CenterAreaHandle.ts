import { _decorator, Component, Node, Sprite } from 'cc';
import { NetworkManager } from '../network/NetworkManager';
import { UnoRoomState } from '../network/schema/BuzUnoRoomState';
import { PokerMeta, PokerColor } from '../network/schema/Poker';
import { PokerComponent } from '../bean/PokerComponent';
const { ccclass, property } = _decorator;

@ccclass('CenterArea')
export class CenterArea extends Component {

    //红色颜色指示器
    @property({ type: Sprite })
    red_indicator: Sprite = null;

    //绿色颜色指示器
    @property({ type: Sprite })
    green_indicator: Sprite = null;

    //黄色颜色指示器
    @property({ type: Sprite })
    yellow_indicator: Sprite = null;

    //蓝色颜色指示器
    @property({ type: Sprite })
    blue_indicator: Sprite = null;

    //卡牌堆
    @property({ type: Node })
    draw_pile: Node = null;

    //弃牌堆
    @property({ type: Node })
    discard_pile: Node = null;

    //BUZUNO按钮
    @property({ type: Node })
    btnBuzUno: Node = null;

    // 网络管理器实例
    private networkManager: NetworkManager;

    // 当前弃牌堆顶部卡牌，用于比较变化
    private currentTopCard: PokerMeta | null = null;

    onEnable() {
        console.log("CenterArea onEnable - 注册网络监听");
        try {
            // 获取网络管理器实例
            this.networkManager = NetworkManager.instance;

            if (!this.networkManager) {
                console.error("CenterArea: NetworkManager 实例获取失败");
                return;
            }

            // 注册房间状态变化监听
            this.networkManager.on('roomStateChanged', this.onRoomStateChanged, this);

            // 首次获取当前房间状态
            this.initializeRoomState();

        } catch (error) {
            console.error("CenterArea: onEnable 时发生错误", error);
        }
    }

    onDisable() {
        console.log("CenterArea onDisable - 销毁网络监听");
        if (this.networkManager) {
            // 销毁房间状态变化监听
            this.networkManager.off('roomStateChanged', this.onRoomStateChanged, this);
        }
    }

    start() {
        // 验证必要的组件是否已设置
        this.validateComponents();
    }

    update(_deltaTime: number) {
        // 暂时不需要在 update 中处理任何逻辑
    }

    /**
     * 验证必要的组件是否已正确设置
     */
    private validateComponents(): void {
        const missingComponents: string[] = [];

        if (!this.red_indicator) missingComponents.push('red_indicator');
        if (!this.green_indicator) missingComponents.push('green_indicator');
        if (!this.blue_indicator) missingComponents.push('blue_indicator');
        if (!this.yellow_indicator) missingComponents.push('yellow_indicator');
        if (!this.discard_pile) missingComponents.push('discard_pile');
        if (!this.draw_pile) missingComponents.push('draw_pile');
        if (!this.btnBuzUno) missingComponents.push('btnBuzUno');

        if (missingComponents.length > 0) {
            console.warn(`CenterArea: 以下组件未设置: ${missingComponents.join(', ')}`);
        } else {
            console.log("CenterArea: 所有必要组件已正确设置");
        }

        // 特别检查 discard_pile 是否有 PokerComponent
        if (this.discard_pile) {
            const pokerComponent = this.discard_pile.getComponent(PokerComponent);
            if (!pokerComponent) {
                console.warn("CenterArea: discard_pile 节点缺少 PokerComponent 组件，卡牌显示可能无法正常工作");
            }
        }
    }

    clickDrawPile() {
        console.log('clickDrawPile')
    }

    clickBuzUno() {
        console.log('clickBuzUno')
    }

    /**
     * 初始化房间状态 - 获取当前房间状态并处理弃牌堆显示
     */
    private initializeRoomState() {
        try {
            if (!this.networkManager) {
                console.error("CenterArea: NetworkManager 未初始化，无法获取房间状态");
                return;
            }

            const currentState = this.networkManager.getCurrentRoomState();
            if (currentState) {
                console.log("CenterArea: 初始化房间状态", currentState);
                this.onRoomStateChanged(currentState);
            } else {
                console.log("CenterArea: 当前没有房间状态数据，初始化为默认状态");
                // 初始化为默认状态：隐藏所有指示器
                this.hideAllColorIndicators();
            }
        } catch (error) {
            console.error("CenterArea: 初始化房间状态时发生错误", error);
        }
    }

    /**
     * 房间状态变化回调函数
     * @param roomState 新的房间状态
     */
    private onRoomStateChanged(roomState: UnoRoomState): void {
        try {
            if (!roomState) {
                console.warn('CenterArea: 接收到空的房间状态');
                return;
            }

            console.log('CenterArea: 房间状态发生变化', roomState);

            // 更新弃牌堆显示
            this.updateDiscardPile(roomState);

            // 更新颜色指示器
            this.updateColorIndicators(roomState);

        } catch (error) {
            console.error('CenterArea: 处理房间状态变化时发生错误', error);
        }
    }

    /**
     * 更新弃牌堆卡牌显示
     * @param roomState 房间状态
     */
    private updateDiscardPile(roomState: UnoRoomState): void {
        try {
            // 验证输入参数
            if (!roomState) {
                console.error("CenterArea: roomState 为空，无法更新弃牌堆");
                return;
            }

            if (!roomState.buzUnoCards) {
                console.warn("CenterArea: buzUnoCards 为空，无法获取弃牌堆数据");
                return;
            }

            // 获取弃牌堆数组
            const discardPile = roomState.buzUnoCards.discardPile;

            if (!discardPile || discardPile.length === 0) {
                console.log("CenterArea: 弃牌堆为空，显示默认状态");
                // 弃牌堆为空时，可以显示卡牌背面或隐藏
                this.currentTopCard = null;
                this.showDefaultCard();
                return;
            }

            // 获取弃牌堆顶部的卡牌（最后一张）
            const topCard = discardPile[discardPile.length - 1];

            if (!topCard) {
                console.warn("CenterArea: 弃牌堆顶部卡牌为空");
                this.showDefaultCard();
                return;
            }

            // 检查是否需要更新（避免不必要的更新）
            if (this.isSameCard(this.currentTopCard, topCard)) {
                console.log("CenterArea: 弃牌堆顶部卡牌未变化，跳过更新");
                return;
            }

            console.log(`CenterArea: 更新弃牌堆顶部卡牌`, topCard);

            // 获取弃牌堆节点的 PokerComponent 组件
            if (!this.discard_pile) {
                console.error("CenterArea: discard_pile 节点未设置");
                return;
            }

            if (!this.discard_pile.isValid) {
                console.error("CenterArea: discard_pile 节点已被销毁");
                return;
            }

            const pokerComponent = this.discard_pile.getComponent(PokerComponent);
            if (!pokerComponent) {
                console.error("CenterArea: discard_pile 节点上未找到 PokerComponent 组件");
                return;
            }

            // 更新卡牌显示
            pokerComponent.setPockerMeta(topCard);

            // 缓存当前顶部卡牌
            this.currentTopCard = topCard;

            console.log("CenterArea: 弃牌堆卡牌显示更新完成");

        } catch (error) {
            console.error("CenterArea: 更新弃牌堆时发生错误", error);
        }
    }

    /**
     * 显示默认卡牌（当弃牌堆为空时）
     */
    private showDefaultCard(): void {
        try {
            if (!this.discard_pile || !this.discard_pile.isValid) {
                return;
            }

            const pokerComponent = this.discard_pile.getComponent(PokerComponent);
            if (pokerComponent) {
                // 显示卡牌背面
                const backCard = PokerMeta.createBackCard();
                pokerComponent.setPockerMeta(backCard);
                console.log("CenterArea: 显示默认卡牌背面");
            }
        } catch (error) {
            console.error("CenterArea: 显示默认卡牌时发生错误", error);
        }
    }

    /**
     * 更新颜色指示器显示
     * @param roomState 房间状态
     */
    private updateColorIndicators(roomState: UnoRoomState): void {
        try {
            // 获取弃牌堆数组
            const discardPile = roomState.buzUnoCards?.discardPile;

            if (!discardPile || discardPile.length === 0) {
                console.log("CenterArea: 弃牌堆为空，隐藏所有颜色指示器");
                this.hideAllColorIndicators();
                return;
            }

            // 获取弃牌堆顶部的卡牌（最后一张）
            const topCard = discardPile[discardPile.length - 1];

            if (!topCard) {
                console.warn("CenterArea: 弃牌堆顶部卡牌为空，隐藏所有颜色指示器");
                this.hideAllColorIndicators();
                return;
            }

            console.log(`CenterArea: 更新颜色指示器，当前卡牌颜色: ${topCard.color}`);

            // 先隐藏所有指示器
            this.hideAllColorIndicators();

            // 根据卡牌颜色显示对应的指示器
            this.showColorIndicator(topCard.color);

        } catch (error) {
            console.error("CenterArea: 更新颜色指示器时发生错误", error);
        }
    }

    /**
     * 隐藏所有颜色指示器
     */
    private hideAllColorIndicators(): void {
        try {
            if (this.red_indicator && this.red_indicator.node) {
                this.red_indicator.node.active = false;
            }
            if (this.green_indicator && this.green_indicator.node) {
                this.green_indicator.node.active = false;
            }
            if (this.blue_indicator && this.blue_indicator.node) {
                this.blue_indicator.node.active = false;
            }
            if (this.yellow_indicator && this.yellow_indicator.node) {
                this.yellow_indicator.node.active = false;
            }

            console.log("CenterArea: 已隐藏所有颜色指示器");
        } catch (error) {
            console.error("CenterArea: 隐藏颜色指示器时发生错误", error);
        }
    }

    /**
     * 显示指定颜色的指示器
     * @param color 要显示的颜色
     */
    private showColorIndicator(color: PokerColor): void {
        try {
            switch (color) {
                case PokerColor.Red:
                    if (this.red_indicator && this.red_indicator.node) {
                        this.red_indicator.node.active = true;
                        console.log("CenterArea: 显示红色指示器");
                    }
                    break;
                case PokerColor.Green:
                    if (this.green_indicator && this.green_indicator.node) {
                        this.green_indicator.node.active = true;
                        console.log("CenterArea: 显示绿色指示器");
                    }
                    break;
                case PokerColor.Blue:
                    if (this.blue_indicator && this.blue_indicator.node) {
                        this.blue_indicator.node.active = true;
                        console.log("CenterArea: 显示蓝色指示器");
                    }
                    break;
                case PokerColor.Yellow:
                    if (this.yellow_indicator && this.yellow_indicator.node) {
                        this.yellow_indicator.node.active = true;
                        console.log("CenterArea: 显示黄色指示器");
                    }
                    break;
                case PokerColor.None:
                case PokerColor.Back:
                    console.log("CenterArea: 万能卡或背面卡，不显示颜色指示器");
                    // 对于万能卡（None）或背面卡（Back），不显示任何指示器
                    break;
                default:
                    console.warn(`CenterArea: 未知的卡牌颜色: ${color}`);
                    break;
            }
        } catch (error) {
            console.error("CenterArea: 显示颜色指示器时发生错误", error);
        }
    }

    /**
     * 比较两张卡牌是否相同
     * @param card1 卡牌1
     * @param card2 卡牌2
     * @returns 是否相同
     */
    private isSameCard(card1: PokerMeta | null, card2: PokerMeta | null): boolean {
        if (card1 === null && card2 === null) return true;
        if (card1 === null || card2 === null) return false;

        return card1.type === card2.type &&
               card1.color === card2.color &&
               card1.value === card2.value;
    }

}

