import {_decorator, assetManager, Component, ImageAsset, Label, Sprite, SpriteFrame, Texture2D} from 'cc';
import { Player, UnoRoomState } from '../network/schema/BuzUnoRoomState';
import { NetworkManager } from '../network/NetworkManager';
import * as i18n from 'db://i18n/LanguageData';

const {ccclass, property} = _decorator;

@ccclass('PrefabOtherUserPlayer')
export class PrefabOtherUserPlayer extends Component {

    @property(Sprite)
    public avatar_node: Sprite = null;

    @property({type: Label})
    public userName: Label = null;

    @property({type: Label})
    public cardNum: Label = null;

    private player?: Player = null;

    // 网络管理器实例
    private networkManager: NetworkManager;

    // 房间状态变化回调函数（需要绑定this）
    private roomStateChangedCallback: (roomState: UnoRoomState) => void;

    onLoad() {
        try {
            // 获取网络管理器实例
            this.networkManager = NetworkManager.instance;

            if (!this.networkManager) {
                console.error('PrefabOtherUserPlayer: 无法获取 NetworkManager 实例');
                return;
            }

            // 绑定回调函数的this上下文
            this.roomStateChangedCallback = this.onRoomStateChanged.bind(this);

            console.log('PrefabOtherUserPlayer: 组件加载完成');
        } catch (error) {
            console.error('PrefabOtherUserPlayer: onLoad 过程中发生错误', error);
        }
    }

    onEnable() {
        console.log("PrefabOtherUserPlayer onEnable - 注册网络监听");
        try {
            if (this.networkManager && this.roomStateChangedCallback) {
                // 注册房间状态变化监听
                this.networkManager.on('roomStateChanged', this.roomStateChangedCallback, this);

                // 首次获取当前房间状态
                this.initializeRoomState();
            }
        } catch (error) {
            console.error("PrefabOtherUserPlayer: onEnable 时发生错误", error);
        }
    }

    onDisable() {
        console.log("PrefabOtherUserPlayer onDisable - 销毁网络监听");
        try {
            if (this.networkManager && this.roomStateChangedCallback) {
                // 销毁房间状态变化监听
                this.networkManager.off('roomStateChanged', this.roomStateChangedCallback, this);
            }
        } catch (error) {
            console.error("PrefabOtherUserPlayer: onDisable 时发生错误", error);
        }
    }

    start() {

    }

    onDestroy() {
        try {
            console.log('PrefabOtherUserPlayer: 开始销毁组件，清理资源');

            // 移除房间状态变化监听，避免内存泄漏
            if (this.networkManager && this.roomStateChangedCallback) {
                this.networkManager.off('roomStateChanged', this.roomStateChangedCallback, this);
                console.log('PrefabOtherUserPlayer: 已移除网络监听器');
            }

            // 清理引用，帮助垃圾回收
            this.player = null;
            this.networkManager = null;
            this.roomStateChangedCallback = null;

            console.log('PrefabOtherUserPlayer: 组件销毁完成，所有资源已清理');
        } catch (error) {
            console.error('PrefabOtherUserPlayer: 销毁组件时发生错误', error);
        }
    }

    update(_deltaTime: number) {
        // 暂时不需要在 update 中处理任何逻辑
    }

    /**
     * 使用 Player 对象初始化组件
     * @param player 玩家数据对象
     */
    public initWithPlayer(player: Player): void {
        if (!player) {
            console.error('PrefabOtherUserPlayer: initWithPlayer 接收到空的 player 对象');
            return;
        }

        this.player = player;

        // 更新用户名显示
        this.updateUserName(player.name);

        // 加载并更新头像
        this.loadAvatar(player.avatar);

        // 更新手牌数量显示
        this.updateCardNumFromRoomState();

        console.log(`PrefabOtherUserPlayer: 初始化玩家 ${player.name} (${player.uid})`);
    }

    /**
     * 更新用户名显示
     * @param name 用户名
     */
    private updateUserName(name: string): void {
        if (this.userName) {
            this.userName.string = name || '未知玩家';
        } else {
            console.warn('GameOtherUserPlayer: userName Label 组件未设置');
        }
    }

    /**
     * 加载并设置头像
     * @param avatarUrl 头像URL
     */
    private loadAvatar(avatarUrl: string): void {
        if (!this.avatar_node) {
            console.warn('GameOtherUserPlayer: avatar_node Sprite 组件未设置');
            return;
        }

        // 如果没有提供头像URL，使用默认头像
        const url = avatarUrl || "https://fastly.picsum.photos/id/1/200/300.jpg?hmac=jH5bDkLr6Tgy3oAg5khKCHeunZMHq0ehBZr6vGifPLY";

        assetManager.loadRemote<ImageAsset>(url, {ext: '.png'}, (err, imageAsset) => {
            if (err) {
                console.error('GameOtherUserPlayer: 加载头像失败', err);
                // 可以在这里设置默认头像
                return;
            }

            if (!imageAsset) {
                console.error('GameOtherUserPlayer: 头像资源为空');
                return;
            }

            try {
                const spriteFrame = new SpriteFrame();
                const texture = new Texture2D();
                texture.image = imageAsset;
                spriteFrame.texture = texture;

                // 确保组件仍然存在（防止在加载过程中组件被销毁）
                if (this.avatar_node && this.avatar_node.isValid) {
                    this.avatar_node.spriteFrame = spriteFrame;
                    console.log(`GameOtherUserPlayer: 头像加载成功 ${this.player?.name}`);
                }
            } catch (error) {
                console.error('GameOtherUserPlayer: 设置头像时发生错误', error);
            }
        });
    }

    /**
     * 更新玩家信息（用于实时同步）
     * @param player 更新的玩家数据
     */
    public updatePlayerInfo(player: Player): void {
        if (!player) {
            console.error('PrefabOtherUserPlayer: updatePlayerInfo 接收到空的 player 对象');
            return;
        }

        const oldPlayer = this.player;
        this.player = player;

        // 检查用户名是否有变化
        if (!oldPlayer || oldPlayer.name !== player.name) {
            this.updateUserName(player.name);
        }

        // 检查头像是否有变化
        if (!oldPlayer || oldPlayer.avatar !== player.avatar) {
            this.loadAvatar(player.avatar);
        }

        // 更新手牌数量显示
        this.updateCardNumFromRoomState();

        console.log(`PrefabOtherUserPlayer: 更新玩家信息 ${player.name} (${player.uid})`);
    }

    /**
     * 获取当前玩家数据
     */
    public getPlayer(): Player | undefined {
        return this.player;
    }

    /**
     * 初始化房间状态 - 获取当前房间状态并处理手牌数据
     */
    private initializeRoomState() {
        try {
            const currentState = this.networkManager.getCurrentRoomState();
            if (currentState) {
                console.log("PrefabOtherUserPlayer: 初始化房间状态", currentState);
                this.onRoomStateChanged(currentState);
            } else {
                console.log("PrefabOtherUserPlayer: 当前没有房间状态数据");
            }
        } catch (error) {
            console.error("PrefabOtherUserPlayer: 初始化房间状态时发生错误", error);
        }
    }

    /**
     * 房间状态变化回调
     * @param roomState 新的房间状态
     */
    private onRoomStateChanged(roomState: UnoRoomState) {
        try {
            console.log("PrefabOtherUserPlayer: 房间状态已更新", roomState);

            // 更新手牌数量显示
            this.updateCardNumFromRoomState();
        } catch (error) {
            console.error("PrefabOtherUserPlayer: 处理房间状态变化时发生错误", error);
        }
    }

    /**
     * 从房间状态更新手牌数量显示
     */
    private updateCardNumFromRoomState() {
        try {
            if (!this.player) {
                console.log("PrefabOtherUserPlayer: 玩家数据未设置，无法更新手牌数量");
                return;
            }

            // 检查网络管理器是否可用
            if (!this.networkManager) {
                console.warn("PrefabOtherUserPlayer: NetworkManager 未初始化，无法更新手牌数量");
                return;
            }

            // 获取当前房间状态
            const roomState = this.networkManager.getCurrentRoomState();
            if (!roomState) {
                console.log("PrefabOtherUserPlayer: 房间状态未获取到，无法更新手牌数量");
                return;
            }

            // 检查 buzUnoCards 是否存在
            if (!roomState.buzUnoCards || !roomState.buzUnoCards.usersCardList) {
                console.warn("PrefabOtherUserPlayer: 房间状态中缺少卡牌数据，无法更新手牌数量");
                this.updateCardNum(0); // 显示0张牌
                return;
            }

            // 从 buzUnoCards.usersCardList 中查找当前玩家的手牌
            const playerCards = roomState.buzUnoCards.usersCardList.find(
                userCards => userCards && userCards.uid === this.player.uid
            );

            if (!playerCards) {
                console.log(`PrefabOtherUserPlayer: 未找到玩家 ${this.player.name} (${this.player.uid}) 的手牌数据`);
                this.updateCardNum(0); // 显示0张牌
                return;
            }

            // 检查 cards 数组是否存在
            const cardCount = playerCards.cards ? playerCards.cards.length : 0;
            console.log(`PrefabOtherUserPlayer: 玩家 ${this.player.name} 手牌数量: ${cardCount}`);

            // 更新UI显示
            this.updateCardNum(cardCount);

        } catch (error) {
            console.error("PrefabOtherUserPlayer: 更新手牌数量时发生错误", error);
            // 发生错误时显示0张牌，避免UI显示异常
            this.updateCardNum(0);
        }
    }

    /**
     * 更新手牌数量显示
     * @param cardCount 手牌数量
     */
    private updateCardNum(cardCount: number) {
        try {
            if (!this.cardNum) {
                console.warn('PrefabOtherUserPlayer: cardNum Label 组件未设置');
                return;
            }

            // 验证 cardCount 参数
            if (typeof cardCount !== 'number' || cardCount < 0) {
                console.warn(`PrefabOtherUserPlayer: 无效的手牌数量 ${cardCount}，使用默认值 0`);
                cardCount = 0;
            }

            // 使用国际化系统显示手牌数量
            const cardText = i18n.tAndReplace("cardNum", { cardNum: cardCount });

            // 检查国际化结果是否有效
            if (!cardText || typeof cardText !== 'string') {
                console.warn('PrefabOtherUserPlayer: 国际化文本获取失败，使用备用显示');
                this.cardNum.string = `${cardCount} cards`;
            } else {
                this.cardNum.string = cardText;
            }

            console.log(`PrefabOtherUserPlayer: 更新手牌数量显示为: ${this.cardNum.string}`);
        } catch (error) {
            console.error("PrefabOtherUserPlayer: 更新手牌数量显示时发生错误", error);
            // 发生错误时使用备用显示
            if (this.cardNum) {
                this.cardNum.string = `${cardCount || 0} cards`;
            }
        }
    }
}

