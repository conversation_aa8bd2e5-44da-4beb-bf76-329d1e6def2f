import { _decorator, Component, Node, Prefab, instantiate } from 'cc';
import { SelectColorClickEnum } from "db://assets/scripts/dlg/DlgSelectColor";
import { NetworkManager } from '../network/NetworkManager';
import { UnoRoomState, Player } from '../network/schema/BuzUnoRoomState';
import { PrefabOtherUserPlayer } from './PrefabOtherUserPlayer';

const { ccclass, property } = _decorator;

@ccclass('GameHandle')
export class GameHandle extends Component {

    /**
     * 选择颜色弹窗
     * 在出万能卡的使用
     */
    @property({ type: Node })
    dlgSelectColor: Node

    @property({ type: Node })
    otherUsersLayout: Node

    @property({ type: Prefab })
    gameOtherUserInfoPrefab: Prefab

    // 网络管理器实例
    private networkManager: NetworkManager;

    // 其他玩家组件映射表 (uid -> GameOtherUserPlayer)
    private otherPlayerComponents: Map<string, PrefabOtherUserPlayer> = new Map();

    // 房间状态变化回调函数（需要绑定this）
    private roomStateChangedCallback: (roomState: UnoRoomState) => void;

    // 上一次的房间状态，用于比较变化
    private lastRoomState: UnoRoomState | null = null;

    onLoad() {
        try {
            // 获取网络管理器实例
            this.networkManager = NetworkManager.instance;

            if (!this.networkManager) {
                console.error('Game: 无法获取 NetworkManager 实例');
                return;
            }

            // 绑定回调函数的this上下文
            this.roomStateChangedCallback = this.onRoomStateChanged.bind(this);

            console.log('Game: 组件加载完成');
        } catch (error) {
            console.error('Game: onLoad 过程中发生错误', error);
        }
    }

    start() {
        try {
            // 验证必要的组件是否已设置
            if (!this.validateComponents()) {
                return;
            }

            // 添加房间状态变化监听
            this.networkManager.on('roomStateChanged', this.roomStateChangedCallback, this);

            // 获取当前房间状态并初始化界面
            const currentRoomState = this.networkManager.getCurrentRoomState();
            if (currentRoomState) {
                this.onRoomStateChanged(currentRoomState);
            }

            console.log('Game: 开始监听房间状态变化');
        } catch (error) {
            console.error('Game: start 过程中发生错误', error);
        }
    }

    /**
     * 验证必要的组件是否已正确设置
     * @returns 验证是否通过
     */
    private validateComponents(): boolean {
        if (!this.networkManager) {
            console.error('Game: NetworkManager 未初始化');
            return false;
        }

        if (!this.otherUsersLayout) {
            console.error('Game: otherUsersLayout 未设置');
            return false;
        }

        if (!this.gameOtherUserInfoPrefab) {
            console.error('Game: gameOtherUserInfoPrefab 未设置');
            return false;
        }

        return true;
    }

    onDestroy() {
        try {
            console.log('Game: 开始销毁组件，清理资源');

            // 移除房间状态变化监听，避免内存泄漏
            if (this.networkManager && this.roomStateChangedCallback) {
                this.networkManager.off('roomStateChanged', this.roomStateChangedCallback, this);
                console.log('Game: 已移除网络监听器');
            }

            // 清理所有其他玩家组件
            this.clearAllOtherPlayers();

            // 清理引用，帮助垃圾回收
            this.networkManager = null;
            this.roomStateChangedCallback = null;
            this.lastRoomState = null;

            console.log('Game: 组件销毁完成，所有资源已清理');
        } catch (error) {
            console.error('Game: 销毁组件时发生错误', error);
        }
    }

    update(_deltaTime: number) {
        // 暂时不需要在 update 中处理任何逻辑
    }

    /**
     * 房间状态变化回调函数
     * @param roomState 新的房间状态
     */
    private onRoomStateChanged(roomState: UnoRoomState): void {
        try {
            if (!roomState) {
                console.warn('Game: 接收到空的房间状态');
                return;
            }

            console.log('Game: 房间状态发生变化', roomState);

            // 检查是否需要更新其他玩家列表
            if (this.shouldUpdateOtherPlayers(roomState)) {
                this.updateOtherPlayersList(roomState.userList);
            }

            // 更新上一次的房间状态
            this.lastRoomState = this.deepCopyRoomState(roomState);
        } catch (error) {
            console.error('Game: 处理房间状态变化时发生错误', error);
        }
    }

    /**
     * 判断是否需要更新其他玩家列表
     * @param newRoomState 新的房间状态
     * @returns 是否需要更新
     */
    private shouldUpdateOtherPlayers(newRoomState: UnoRoomState): boolean {
        // 如果是第一次接收状态，需要更新
        if (!this.lastRoomState) {
            return true;
        }

        // 比较用户列表是否有变化
        const lastOtherPlayers = this.lastRoomState.userList.filter(player => !player.isMe);
        const newOtherPlayers = newRoomState.userList.filter(player => !player.isMe);

        // 检查玩家数量是否变化
        if (lastOtherPlayers.length !== newOtherPlayers.length) {
            console.log('Game: 其他玩家数量发生变化，需要更新');
            return true;
        }

        // 检查玩家信息是否有变化
        for (const newPlayer of newOtherPlayers) {
            const lastPlayer = lastOtherPlayers.find(p => p.uid === newPlayer.uid);
            if (!lastPlayer) {
                console.log(`Game: 发现新玩家 ${newPlayer.name}，需要更新`);
                return true;
            }

            // 检查玩家信息是否有变化
            if (this.hasPlayerInfoChanged(lastPlayer, newPlayer)) {
                console.log(`Game: 玩家 ${newPlayer.name} 信息发生变化，需要更新`);
                return true;
            }
        }

        return false;
    }

    /**
     * 检查玩家信息是否有变化
     * @param oldPlayer 旧的玩家信息
     * @param newPlayer 新的玩家信息
     * @returns 是否有变化
     */
    private hasPlayerInfoChanged(oldPlayer: Player, newPlayer: Player): boolean {
        return oldPlayer.name !== newPlayer.name ||
               oldPlayer.avatar !== newPlayer.avatar ||
               oldPlayer.stateWrapper.state !== newPlayer.stateWrapper.state;
    }

    /**
     * 深拷贝房间状态（简化版本，只拷贝需要的字段）
     * @param roomState 原始房间状态
     * @returns 拷贝的房间状态
     */
    private deepCopyRoomState(roomState: UnoRoomState): UnoRoomState {
        const copy = new UnoRoomState();
        copy.currenScene = roomState.currenScene;
        copy.currentTurn = roomState.currentTurn;
        copy.userList = roomState.userList.map(player => new Player(
            player.name,
            player.uid,
            player.avatar,
            { ...player.stateWrapper },
            player.isMe
        ));
        return copy;
    }

    /**
     * 更新其他玩家列表显示
     * @param userList 用户列表
     */
    private updateOtherPlayersList(userList: Player[]): void {
        try {
            if (!userList || !Array.isArray(userList)) {
                console.warn('Game: 用户列表为空或格式错误');
                this.clearAllOtherPlayers();
                return;
            }

            // 过滤出其他玩家（排除当前玩家）
            const otherPlayers = userList.filter(player => {
                if (!player || typeof player.uid !== 'string') {
                    console.warn('Game: 发现无效的玩家数据', player);
                    return false;
                }
                return !player.isMe;
            });

            console.log(`Game: 发现 ${otherPlayers.length} 个其他玩家`);

            // 获取当前已存在的玩家UID集合
            const currentPlayerUids = new Set(this.otherPlayerComponents.keys());

            // 获取新的玩家UID集合
            const newPlayerUids = new Set(otherPlayers.map(player => player.uid));

            // 移除不再存在的玩家组件
            for (const uid of currentPlayerUids) {
                if (!newPlayerUids.has(uid)) {
                    this.removeOtherPlayer(uid);
                }
            }

            // 添加或更新玩家组件
            for (const player of otherPlayers) {
                try {
                    if (this.otherPlayerComponents.has(player.uid)) {
                        // 更新现有玩家信息
                        this.updateOtherPlayer(player);
                    } else {
                        // 创建新的玩家组件
                        this.createOtherPlayer(player);
                    }
                } catch (playerError) {
                    console.error(`Game: 处理玩家 ${player.uid} 时发生错误`, playerError);
                }
            }
        } catch (error) {
            console.error('Game: 更新其他玩家列表时发生错误', error);
        }
    }

    /**
     * 创建其他玩家组件
     * @param player 玩家数据
     */
    private createOtherPlayer(player: Player): void {
        if (!this.gameOtherUserInfoPrefab) {
            console.error('Game: gameOtherUserInfoPrefab 预制体未设置');
            return;
        }

        if (!this.otherUsersLayout) {
            console.error('Game: otherUsersLayout 容器未设置');
            return;
        }

        try {
            // 实例化预制体
            const playerNode = instantiate(this.gameOtherUserInfoPrefab);

            // 添加到布局容器
            this.otherUsersLayout.addChild(playerNode);

            // 获取 GameOtherUserPlayer 组件
            const gameOtherUserPlayer = playerNode.getComponent(PrefabOtherUserPlayer);
            if (gameOtherUserPlayer) {
                // 初始化玩家信息
                gameOtherUserPlayer.initWithPlayer(player);

                // 存储组件引用
                this.otherPlayerComponents.set(player.uid, gameOtherUserPlayer);

                console.log(`Game: 创建其他玩家组件成功 ${player.name} (${player.uid})`);
            } else {
                console.error('Game: gameOtherUserInfoPrefab 中没有找到 GameOtherUserPlayer 组件');
                playerNode.destroy();
            }
        } catch (error) {
            console.error('Game: 创建其他玩家组件时发生错误', error);
        }
    }

    /**
     * 更新其他玩家信息
     * @param player 玩家数据
     */
    private updateOtherPlayer(player: Player): void {
        const component = this.otherPlayerComponents.get(player.uid);
        if (component && component.isValid) {
            component.updatePlayerInfo(player);
            console.log(`Game: 更新其他玩家信息 ${player.name} (${player.uid})`);
        } else {
            console.warn(`Game: 找不到玩家组件 ${player.uid}，尝试重新创建`);
            this.otherPlayerComponents.delete(player.uid);
            this.createOtherPlayer(player);
        }
    }

    /**
     * 移除其他玩家组件
     * @param uid 玩家UID
     */
    private removeOtherPlayer(uid: string): void {
        try {
            const component = this.otherPlayerComponents.get(uid);
            if (component) {
                // 检查组件是否仍然有效
                if (component.isValid && component.node && component.node.isValid) {
                    component.node.destroy();
                    console.log(`Game: 移除其他玩家组件 ${uid}`);
                } else {
                    console.warn(`Game: 玩家组件 ${uid} 已经无效，跳过销毁`);
                }
            }

            // 无论如何都要从映射表中移除
            this.otherPlayerComponents.delete(uid);
        } catch (error) {
            console.error(`Game: 移除玩家组件 ${uid} 时发生错误`, error);
            // 确保从映射表中移除，即使销毁失败
            this.otherPlayerComponents.delete(uid);
        }
    }

    /**
     * 清理所有其他玩家组件
     */
    private clearAllOtherPlayers(): void {
        try {
            console.log(`Game: 开始清理 ${this.otherPlayerComponents.size} 个其他玩家组件`);

            const uidsToRemove: string[] = [];

            for (const [uid, component] of this.otherPlayerComponents) {
                try {
                    if (component && component.isValid && component.node && component.node.isValid) {
                        component.node.destroy();
                    }
                    uidsToRemove.push(uid);
                } catch (componentError) {
                    console.error(`Game: 销毁玩家组件 ${uid} 时发生错误`, componentError);
                    uidsToRemove.push(uid); // 即使销毁失败也要从映射表中移除
                }
            }

            // 清理映射表
            for (const uid of uidsToRemove) {
                this.otherPlayerComponents.delete(uid);
            }

            // 最后确保映射表完全清空
            this.otherPlayerComponents.clear();

            console.log('Game: 清理所有其他玩家组件完成');
        } catch (error) {
            console.error('Game: 清理其他玩家组件时发生错误', error);
            // 强制清空映射表
            this.otherPlayerComponents.clear();
        }
    }

    /**
     * dlgSelectColor 点击事件处理
     * @param type
     */
    selectColorDlg(type: SelectColorClickEnum) {
        console.log(`selectColorDlg type ${type}`)
        if (type === SelectColorClickEnum.CLOSE) {
            this.dlgSelectColor.active = false;

        }
    }

   
}

