import { _decorator, Component, Label, Node, Prefab, instantiate, Animation, Sprite, Vec3 } from 'cc';
import { NetworkManager } from '../network/NetworkManager';
import { UnoRoomState, Player, UserState } from '../network/schema/BuzUnoRoomState';
import { WaitingUserCom } from './WaitingUserCom';
import * as i18n from 'db://i18n/LanguageData';

const { ccclass, property } = _decorator;

@ccclass('WaitingRoomMain')
export class WaitingRoomMain extends Component {

    @property({ type: Node })
    readyBtn: Node;

    @property({ type: Node })
    playersLayout: Node;

    @property({ type: Prefab })
    playerPrefab: Prefab;

    @property({ type: Label })
    subTitle: Label;

    
    @property({ type: Label })
    readyLable: Label;

    // 网络管理器实例
    private networkManager: NetworkManager;

    // 存储已创建的用户组件，key为用户uid，value为WaitingUserCom组件
    private userComponents: Map<string, WaitingUserCom> = new Map();

    onLoad() {
        console.log("WaitingRoomMain load");
        this.networkManager = NetworkManager.instance;

        // 确保国际化模块已初始化
        if (!i18n.ready) {
            i18n.init('zh'); // 默认使用中文
            i18n.updateSceneRenderers();

        }
    }

    onEnable() {
        console.log("WaitingRoomMain onEnable - 注册网络监听");
        // 注册房间状态变化监听
        this.networkManager.on('roomStateChanged', this.onRoomStateChanged, this);

        // 首次获取当前房间状态
        this.initializeRoomState();
    }

    onDisable() {
        console.log("WaitingRoomMain onDisable - 销毁网络监听");
        // 销毁房间状态变化监听
        this.networkManager.off('roomStateChanged', this.onRoomStateChanged, this);
    }

    start() {

    }

    /**
     * 初始化房间状态 - 获取当前房间状态并处理用户列表
     */
    private initializeRoomState() {
        const currentState = this.networkManager.getCurrentRoomState();
        if (currentState) {
            console.log("初始化房间状态:", currentState);
            this.onRoomStateChanged(currentState);
        } else {
            console.log("当前没有房间状态数据");
        }
    }

    /**
     * 房间状态变化回调
     * @param roomState 新的房间状态
     */
    private onRoomStateChanged(roomState: UnoRoomState) {
        console.log("房间状态已更新:", roomState);
        // TODO: 处理用户列表更新逻辑
        this.updateUserList(roomState.userList);


        roomState.userList.filter(user => user.isMe).forEach(user => {
            this.updateReadButton(user.stateWrapper.state === UserState.READY);
        })
    }

    updateReadButton(ready: boolean) {
        if (ready) {
            this.readyBtn.getComponent(Animation).stop()
            this.readyBtn.getComponent(Sprite).grayscale=true
            this.readyLable.string = i18n.t("readied")
            this.readyBtn.scale=new Vec3(1, 1, 1)
        }else{
            this.readyBtn.getComponent(Sprite).grayscale=false
            this.readyBtn.getComponent(Animation).play()
            this.readyLable.string = i18n.t("ready")
        }
    }

    /**
     * 更新用户列表显示
     * @param userList 用户列表
     */
    private updateUserList(userList: Player[]) {
        console.log("更新用户列表:", userList);

        // 获取当前用户列表中的所有uid
        const currentUserUids = new Set(userList.map(user => user.uid));

        // 删除不存在的用户组件
        this.removeDeletedUsers(currentUserUids);

        // 添加或更新用户组件
        this.addOrUpdateUsers(userList);
    }

    /**
     * 删除不存在的用户组件
     * @param currentUserUids 当前用户uid集合
     */
    private removeDeletedUsers(currentUserUids: Set<string>) {
        const toDelete: string[] = [];

        // 找出需要删除的用户
        this.userComponents.forEach((component, uid) => {
            if (!currentUserUids.has(uid)) {
                toDelete.push(uid);
            }
        });

        // 删除用户组件
        toDelete.forEach(uid => {
            const component = this.userComponents.get(uid);
            if (component && component.node) {
                console.log(`删除用户组件: ${uid}`);
                component.node.destroy();
                this.userComponents.delete(uid);
            }
        });
    }

    /**
     * 添加或更新用户组件
     * @param userList 用户列表
     */
    private addOrUpdateUsers(userList: Player[]) {
      this.subTitle.string= i18n.tAndReplace("waitingRoomSubtitle",{
            ready: `${userList.filter(user => user.stateWrapper.state === UserState.READY).length}`,
            total: `${userList.length}`
        });
       
        userList.forEach(user => {
            const existingComponent = this.userComponents.get(user.uid);

            if (existingComponent) {
                // 更新现有用户信息
                console.log(`更新用户信息: ${user.uid}`);
                this.updateUserComponent(existingComponent, user);
            } else {
                // 创建新用户组件
                console.log(`创建新用户组件: ${user.uid}`);
                this.createUserComponent(user);
            }
        });
    }

    /**
     * 创建用户组件
     * @param user 用户信息
     */
    private createUserComponent(user: Player) {
        if (!this.playerPrefab) {
            console.error("playerPrefab 未设置");
            return;
        }

        // 实例化预制体
        const playerNode = instantiate(this.playerPrefab);

        // 添加到布局容器
        this.playersLayout.addChild(playerNode);

        // 获取 WaitingUserCom 组件
        const waitingUserCom = playerNode.getComponent(WaitingUserCom);
        if (waitingUserCom) {
            // 更新用户信息
            this.updateUserComponent(waitingUserCom, user);

            // 存储组件引用
            this.userComponents.set(user.uid, waitingUserCom);

            console.log(`用户组件创建成功: ${user.uid}`);
        } else {
            console.error("playerPrefab 中没有找到 WaitingUserCom 组件");
            playerNode.destroy();
        }
    }

    /**
     * 更新用户组件信息
     * @param component WaitingUserCom组件
     * @param user 用户信息
     */
    private updateUserComponent(component: WaitingUserCom, user: Player) {
        console.log(`更新用户组件信息: ${user.uid}`, user);

        // 调用 WaitingUserCom 的 updateUserInfo 方法
        component.updateUserInfo(user);
    }

    clickReadyBtn() {
        console.log("点击准备按钮");
        this.networkManager.changeReadState();
    }

    update(deltaTime: number) {

    }
}

