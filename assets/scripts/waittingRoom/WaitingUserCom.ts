import { _decorator, Component, Label, Node, Sprite } from 'cc';
import { Player, UserState } from '../network/schema/BuzUnoRoomState';
import { AvatarLoader } from '../utils/AvatarLoader';
const { ccclass, property } = _decorator;

@ccclass('WaitingUserCom')
export class WaitingUserCom extends Component {


    @property({ type: Node })
    avatarNode: Node


    @property({ type: Label })
    userNameLable: Label


    @property({ type: Label })
    readyStateLable: Label

    @property({ type: Node })
    meTip: Node

    userInfo?: Player

    protected onLoad(): void {
        console.log("WaitingUserCom onLoad");
    }

    /**
     * 更新用户信息
     * @param user 用户信息
     */
    updateUserInfo(user: Player) {
        this.userInfo = user;

        // 更新用户名显示
        this.updateUserName(user.name);

        // 更新准备状态显示
        this.updateReadyState(user.stateWrapper.state);

        // 加载头像
        this.loadAvatar(user.avatar);

        this.updateMeState(user);
    }
    updateMeState(user: Player) {
      this.meTip.active = user.isMe;
    }

    /**
     * 更新用户名显示
     * @param userName 用户名
     */
    private updateUserName(userName: string) {
        if (this.userNameLable) {
            this.userNameLable.string = userName || "未知用户";
        }
    }

    /**
     * 更新准备状态显示
     * @param state 用户状态
     */
    private updateReadyState(state: number) {
        if (!this.readyStateLable) {
            return;
        }

        switch (state) {
            case UserState.READY:
                this.readyStateLable.string = "已准备";
                this.node.getComponent(Sprite).grayscale = false
                break;
            default:
                this.node.getComponent(Sprite).grayscale = true
                this.readyStateLable.string = "未准备";
                break;
        }
    }

    /**
     * 加载用户头像
     * @param avatarUrl 头像URL
     */
    private loadAvatar(avatarUrl: string) {
        if (!this.avatarNode || !avatarUrl) {
            console.log("WaitingUserCom: avatarNode 或 avatarUrl 为空，跳过头像加载");
            return;
        }

        AvatarLoader.loadAvatar(
            avatarUrl,
            this.avatarNode,
            () => {
                console.log(`WaitingUserCom: 头像加载成功 ${avatarUrl}`);
            },
            (error) => {
                console.error(`WaitingUserCom: 头像加载失败 ${avatarUrl}`, error);
                // 可以在这里设置默认头像
                // AvatarLoader.setDefaultAvatar(this.avatarNode, defaultSpriteFrame);
            }
        );
    }


    start() {
        console.log("WaitingUserCom start - 属性初始化检查:");
        console.log("  avatarNode:", this.avatarNode);
        console.log("  userNameLable:", this.userNameLable);
        console.log("  readyStateLable:", this.readyStateLable);
    }

    update(deltaTime: number) {

    }
}

