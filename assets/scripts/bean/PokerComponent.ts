import { _decorator, Component, Node, Sprite, Sprite<PERSON><PERSON><PERSON> } from 'cc';
import { PokerMeta } from '../network/schema/Poker';
import { PockerGenerate } from '../utils/PockerGenerate';
const { ccclass, property } = _decorator;
import * as i18n from 'db://i18n/LanguageData';

@ccclass('PokerComponent')
export class PokerComponent extends Component {
  
    private pockerMeta?: PokerMeta = null

    @property({type:SpriteAtlas})
    carsAtlas:SpriteAtlas

    start() {
    }

    
    
   

    setPockerMeta(pockerMeta: PokerMeta) {
        this.pockerMeta = pockerMeta;

        this.updateUI(pockerMeta);
    }


    update(deltaTime: number) {
       
    }

    updateUI(pockerMeta: PokerMeta) {
        if (!pockerMeta) {
            return;
        }

        this.getComponent(Sprite).spriteFrame =  this.carsAtlas.getSpriteFrame(PockerGenerate.generateResourceName(pockerMeta));;
    }

}

