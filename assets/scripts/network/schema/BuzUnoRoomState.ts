import { ArraySchema, Schema, type } from "@colyseus/schema";

/**
 * 扑克牌颜色枚举
 */
export enum PokerColor {
    Red = 0,      // 红色
    Green = 1,    // 绿色
    Blue = 2,     // 蓝色
    Yellow = 3,   // 黄色
    None = 4,     // 万能卡未选择颜色时的状态
    Back = 5      // 卡牌背面
}

/**
 * 扑克牌类型枚举
 */
export enum PokerType {
    Number = 0,       // 数字卡 (0-9)
    Skip = 1,         // 跳过下一个玩家
    Reverse = 2,      // 反转游戏方向
    DrawTwo = 3,      // +2 卡（下一个玩家抽2张牌）
    Wild = 4,         // 万能卡（可以改变颜色）
    WildDrawFour = 5, // 万能+4卡
    Back = 6          // 卡牌背面
}

/**
 * 扑克牌元数据类 - 继承自 Colyseus Schema，支持网络同步
 */
export class PokerMeta extends Schema {
    @type("int32") type: PokerType;
    @type("int32") color: PokerColor;
    @type("int32") value: number; // 数字卡的数值（0-9），其他类型卡为-1
    @type("string") resourceName: string; // 根据卡牌属性生成的资源名称，用于前端显示

    constructor(type: PokerType = PokerType.Back,
                color: PokerColor = PokerColor.Back,
                value: number = -1) {
        super();
        this.type = type;
        this.color = color;
        this.value = value;
        this.resourceName = this.generateResourceName();
    }

    /**
     * 生成资源名称
     * 根据卡牌类型和颜色生成对应的资源文件名
     */
    private generateResourceName(): string {
        if (this.type === PokerType.Back) {
            return "bg";
        }

        if (this.type === PokerType.Wild) {
            return "w_1";
        }

        if (this.type === PokerType.WildDrawFour) {
            return "w_4";
        }

        // 普通颜色卡牌
        let colorPrefix = "";
        switch (this.color) {
            case PokerColor.Blue:
                colorPrefix = "b";
                break;
            case PokerColor.Red:
                colorPrefix = "r";
                break;
            case PokerColor.Yellow:
                colorPrefix = "y";
                break;
            case PokerColor.Green:
                colorPrefix = "g";
                break;
            default:
                colorPrefix = "bg";
                break;
        }

        let valueStr = "";
        if (this.type === PokerType.Number) {
            valueStr = this.value.toString();
        } else if (this.type === PokerType.Skip) {
            valueStr = "10";
        } else if (this.type === PokerType.Reverse) {
            valueStr = "11";
        } else if (this.type === PokerType.DrawTwo) {
            valueStr = "12";
        }

        return `${colorPrefix}_${valueStr}`;
    }

    /**
     * 判断当前卡牌是否可以打在指定卡牌上
     * @param topCard 当前桌面顶部的卡牌
     * @returns 是否可以打出
     */
    canPlayOn(topCard: PokerMeta): boolean {
        // 万能卡可以在任何时候打出
        if (this.type === PokerType.Wild || this.type === PokerType.WildDrawFour) {
            return true;
        }

        // 普通卡牌必须满足以下条件之一：
        // 1. 颜色相同
        if (this.color === topCard.color) {
            return true;
        }

        // 2. 数字卡且数值相同
        if (this.type === PokerType.Number && topCard.type === PokerType.Number && this.value === topCard.value) {
            return true;
        }

        // 3. 类型相同（非数字卡）
        if (this.type !== PokerType.Number && this.type === topCard.type) {
            return true;
        }

        return false;
    }

    /**
     * 为万能卡设置颜色（在玩家选择颜色后调用）
     * @param color 要设置的颜色
     */
    setWildColor(color: PokerColor): void {
        if (this.type === PokerType.Wild || this.type === PokerType.WildDrawFour) {
            this.color = color;
            // 更新资源名称（万能卡的资源名称不变，但颜色会影响游戏逻辑）
        }
    }

    /**
     * 创建一张背面卡牌
     * @returns 背面卡牌实例
     */
    static createBackCard(): PokerMeta {
        return new PokerMeta(PokerType.Back, PokerColor.Back, -1);
    }

    /**
     * 创建万能卡
     * @param isDrawFour 是否为万能+4卡，默认为false
     * @returns 万能卡实例（初始颜色为None）
     */
    static createWildCard(isDrawFour: boolean = false): PokerMeta {
        const type = isDrawFour ? PokerType.WildDrawFour : PokerType.Wild;
        return new PokerMeta(type, PokerColor.None, -1);
    }
}
/**
 * 游戏阶段枚举
 */
export enum GamePhase {
    // 等待玩家 所有玩家准备好后且人数大于等于 2 进入PLAYING阶段
    WAITING = 0,
    // 游戏进行中
    PLAYING = 1,
    //游戏结束展示VictoryInfo信息 5s 后重新进入WAITING
    GAME_END = 5        // 游戏结束
}

/**
 * 玩家游戏状态枚举
 */
export enum PlayerGameState {
    NOT_READY = 0,      // 未准备
    READY = 1,          // 已准备
    PLAYING  = 3,      // 游戏中
    WATCHING = 4        // 观战模式
}

/**
 * 游戏方向枚举
 */
export enum GameDirection {
    CLOCK_WISE = 0,      // 顺时针
    COUNTER_CLOCK_WISE = 1 // 逆时针
}

/**
 * 用户状态枚举
 */
export enum UserState {
    NOT_READY = 0,      // 未准备
    READY = 1,          // 已准备
    PLAYING = 3,        // 游戏中
    WATCHING = 4        // 观战模式
}

/**
 * 用户状态包装器 - Schema 类
 */
export class UserStateWrapper extends Schema {
    @type("int32") state: UserState = UserState.NOT_READY;

    constructor(state: UserState = UserState.NOT_READY) {
        super();
        this.state = state;
    }
}

/**
 * 用户卡牌类 - Schema 类
 */
export class UserCards extends Schema {
    @type("string") uid: string = "";
    @type([PokerMeta]) cards = new ArraySchema<PokerMeta>();

    constructor(uid: string = "") {
        super();
        this.uid = uid;
    }
}

/**
 * 玩家信息类 - Schema 类
 */
export class Player extends Schema {
    @type("string") uid: string;
    @type("string") name: string;
    @type("string") avatar: string;
    @type("boolean") unoDeclared: boolean = false; // 是否喊了UNO
    @type("int32") playerGameState: PlayerGameState = PlayerGameState.NOT_READY;
    @type([PokerMeta]) cards = new ArraySchema<PokerMeta>(); // 手牌
    @type("string") buzWatchID: string = ""; // 观众模式时指向的玩家ID
    @type(UserStateWrapper) stateWrapper: UserStateWrapper;

    constructor(name: string = "", uid: string = "", avatar: string = "", stateWrapper?: UserStateWrapper) {
        super();
        this.uid = uid;
        this.name = name;
        this.avatar = avatar;
        this.unoDeclared = false;
        this.playerGameState = PlayerGameState.NOT_READY;
        this.buzWatchID = "";
        this.stateWrapper = stateWrapper || new UserStateWrapper();
    }
}

/**
 * 游戏卡牌管理类 - Schema 类
 */
export class BuzUnoCards extends Schema {
    @type([PokerMeta]) cardPile = new ArraySchema<PokerMeta>(); // 抽牌堆
    @type([PokerMeta]) discardPile = new ArraySchema<PokerMeta>(); // 弃牌堆
    @type([UserCards]) usersCardList = new ArraySchema<UserCards>(); // 玩家手牌列表

    constructor() {
        super();
    }
}

/**
 * UNO 房间状态类 - 主状态 Schema 类
 */
export class UnoRoomState extends Schema {

    @type([Player]) userList = new ArraySchema<Player>(); // 玩家列表
    @type("string") currentTurn: string = ""; // 当前回合玩家ID
    @type(BuzUnoCards) buzUnoCards: BuzUnoCards; // 卡牌相关数据
    @type("int32") gameDirection: GameDirection = GameDirection.CLOCK_WISE; // 游戏方向
    @type("int32") gamePhase: GamePhase = GamePhase.WAITING; // 游戏阶段

    constructor() {
        super();
        this.buzUnoCards = new BuzUnoCards();
    }
}

