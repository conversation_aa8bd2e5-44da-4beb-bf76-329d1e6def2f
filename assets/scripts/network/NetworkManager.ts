// assets/scripts/network/NetworkManager.ts

import {EventTarget} from 'cc'; // 可选：使用 Cocos 事件系统
import Colyseus from 'db://colyseus-sdk/colyseus.js';
import {_decorator, Component, Label, Node} from 'cc';
import {UnoRoomState, Player, UserState, UserCards} from './schema/BuzUnoRoomState';
import {PokerMeta} from './schema/Poker';
import {BuzUnoRoomEvent} from './ServerEvents';
import { SceneRouter } from '../utils/SceneRouter';

export class NetworkManager {
    //localhost:2567
    hostname = "localhost";
    port = 2567;
    useSSL = false;
    client!: Colyseus.Client;
    room!: Colyseus.Room;

    // 缓存当前房间状态
    private currentRoomState?: UnoRoomState;

    // 👇 核心：事件分发器
    private eventTarget = new EventTarget();

    // 👇 对外暴露：注册监听
    public on<T = any>(eventName: string, callback: (data: T) => void, target?: any) {
        this.eventTarget.on(eventName, callback, target);
    }

    // 👇 对外暴露：取消监听
    public off(eventName: string, callback: Function, target?: any) {
        this.eventTarget.off(eventName, callback as (...args: any[]) => void, this)
    }

    // 👇 对外暴露：获取当前房间状态
    public getCurrentRoomState(): UnoRoomState | null {
        return this.currentRoomState;
    }

    // 👇 单例核心
    private static _instance: NetworkManager;
    public static get instance(): NetworkManager {
        if (!NetworkManager._instance) {
            NetworkManager._instance = new NetworkManager();
        }
        return NetworkManager._instance;
    }

    // 私有构造器，禁止外部 new
    private constructor() {
        console.log('🌐 NetworkManager created');
        this.client = new Colyseus.Client(`${this.useSSL ? "wss" : "ws"}://${this.hostname}${([443, 80].includes(this.port) || this.useSSL) ? "" : `:${this.port}`}`);
        // Connect into the room
        this.connect();

    }



    private async connect() {
        try {
            this.room = await this.client.joinOrCreate("buz_uno_room");

            console.log("joined successfully!");
            console.log("user's sessionId:", this.room.sessionId);

            this.room.onStateChange((state) => {
                //将 state 对象转化为UnoRoomState对象
                console.log("onStateChange:", JSON.stringify(state));
                try {
                    const unoRoomState = new UnoRoomState();

                    // 转换用户列表
                    if (state.userList && state.userList.items != null && Array.isArray(state.userList.items)) {
                        unoRoomState.userList = state.userList.items.map((user: any) => {
                            const userState = new UserState();
                            userState.state = user.stateWrapper?.state ?? UserState.NOT_READY;
                            userState.watchUid = user.stateWrapper?.watchUid;

                            return new Player(
                                user.name || '',
                                user.uid || '',
                                user.avatar || '',
                                userState,
                                user.isMe || false
                            );
                        });
                    }

                    // 设置当前轮到的用户
                    if (state.currentTurn) {
                        unoRoomState.currentTurn = state.currentTurn;
                    }
                    if (state.currenScene!=null) {
                        unoRoomState.currenScene=state.currenScene
                        SceneRouter.instance.gotoScene(state.currenScene)
                    }

                    // 处理 buzUnoCards 数据
                    if (state.buzUnoCards) {
                        // 处理牌堆区 (cardPile)
                        if (state.buzUnoCards.cardPile && state.buzUnoCards.cardPile.items && Array.isArray(state.buzUnoCards.cardPile.items)) {
                            unoRoomState.buzUnoCards.cardPile = state.buzUnoCards.cardPile.items.map((card: any) => {
                                const pokerMeta = new PokerMeta(card.type, card.color, card.value);
                                return pokerMeta;
                            });
                        }

                        // 处理弃牌区 (discardPile)
                        if (state.buzUnoCards.discardPile && state.buzUnoCards.discardPile.items && Array.isArray(state.buzUnoCards.discardPile.items)) {
                            unoRoomState.buzUnoCards.discardPile = state.buzUnoCards.discardPile.items.map((card: any) => {
                                const pokerMeta = new PokerMeta(card.type, card.color, card.value);
                                return pokerMeta;
                            });
                        }

                        // 处理用户卡牌列表 (usersCardList)
                        if (state.buzUnoCards.usersCardList && state.buzUnoCards.usersCardList.items && Array.isArray(state.buzUnoCards.usersCardList.items)) {
                            unoRoomState.buzUnoCards.usersCardList = state.buzUnoCards.usersCardList.items.map((userCards: any) => {
                                const userCardsObj = new UserCards();
                                userCardsObj.uid = userCards.uid || '';

                                // 处理用户的卡牌数组
                                if (userCards.cards && userCards.cards.items && Array.isArray(userCards.cards.items)) {
                                    userCardsObj.cards = userCards.cards.items.map((card: any) => {
                                        const pokerMeta = new PokerMeta(card.type, card.color, card.value);
                                        return pokerMeta;
                                    });
                                }

                                return userCardsObj;
                            });
                        }
                    }

                    // 缓存转换后的状态对象
                    this.currentRoomState = unoRoomState;

                    // 👇 触发自定义事件，通知监听者房间状态已更新
                    this.eventTarget.emit('roomStateChanged', unoRoomState);

                    console.log("onStateChange: 成功转换为UnoRoomState", unoRoomState);
                } catch (error) {
                    console.error("转换state对象为UnoRoomState时发生错误:", error);
                    console.log("原始state数据:", JSON.stringify(state));
                }
            });

            this.room.onLeave((code) => {
                console.log("onLeave:", code);
            });

        } catch (e) {
            console.error(e);
        }
    }

    changeReadState() {
        this.room.send(BuzUnoRoomEvent.EVENT_READY_CHANGE);
    }
}