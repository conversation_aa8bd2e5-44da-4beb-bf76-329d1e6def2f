<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>b_0.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{1,1},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>b_1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{243,1},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>b_10.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{2421,1},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>b_11.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{1,363},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>b_12.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{243,363},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>b_2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{485,1},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>b_3.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{727,1},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>b_4.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{969,1},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>b_5.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{1211,1},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>b_6.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{1453,1},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>b_7.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{1695,1},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>b_8.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{1937,1},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>b_9.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{2179,1},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>bg.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{485,363},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>g_0.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{727,363},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>g_1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{969,363},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>g_10.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{485,725},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>g_11.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{727,725},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>g_12.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{969,725},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>g_2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{1211,363},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>g_3.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{1453,363},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>g_4.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{1695,363},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>g_5.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{1937,363},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>g_6.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{2179,363},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>g_7.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{2421,363},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>g_8.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{1,725},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>g_9.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{243,725},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>r_0.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{1211,725},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>r_1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{1453,725},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>r_10.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{969,1087},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>r_11.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{1211,1087},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>r_12.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{1453,1087},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>r_2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{1695,725},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>r_3.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{1937,725},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>r_4.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{2179,725},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>r_5.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{2421,725},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>r_6.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{1,1087},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>r_7.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{243,1087},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>r_8.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{485,1087},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>r_9.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{727,1087},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>w_1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{1695,1087},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>w_4.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{1937,1087},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>y_0.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{2179,1087},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>y_1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{2421,1087},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>y_10.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{1937,1449},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>y_11.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{2179,1449},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>y_12.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{2421,1449},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>y_2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{1,1449},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>y_3.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{243,1449},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>y_4.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{485,1449},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>y_5.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{727,1449},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>y_6.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{969,1449},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>y_7.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{1211,1449},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>y_8.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{1453,1449},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>y_9.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{240,360}</string>
                <key>spriteSourceSize</key>
                <string>{240,360}</string>
                <key>textureRect</key>
                <string>{{1695,1449},{240,360}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>buz_poker_cards.png</string>
            <key>size</key>
            <string>{2662,1810}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:28af9970c735161038d0438613230f69:7a859a5e0810af1f63fb31eda8ab205d:ff4acf28e464da23e2deb7b290c313ab$</string>
            <key>textureFileName</key>
            <string>buz_poker_cards.png</string>
        </dict>
    </dict>
</plist>
