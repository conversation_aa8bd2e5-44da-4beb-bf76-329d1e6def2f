---
type: "always_apply"
---

# 概述

这是一个 Cocos2D 游戏项目，主要用于完成一个叫做 UNO 的游戏。

# 卡片资源说明

项目有buz_pocker_cards.plist精灵表，里面存放了所有的卡片资源。每个卡片资源的文件名都是 `<type>_<value>`.
 type 类型如下枚举
 1. b - 蓝色
 2. r - 红色
 3. y - 黄色
 4. g - 绿色
 5. w - 万能卡
 6. bg - 卡牌背景
如果 type 为 b,r,y,g话value 有如下规则:
value 数字范围 0-12
 1. 0-9 数字卡
 2. 10 - 跳过卡
 3. 11 - 反转卡
 4. 12 - +2 卡

如果 type 为 w话value 有如下规则:
1. 1 普通万能卡牌
2. 4 +4 万能卡牌

如果 type 为 bg话value无数值.

## 举例说明
比如: b_1 蓝色数字牌1,b_10 蓝色跳过卡,r_0 红色色数字牌1,bg 卡牌背景, w_1 普通万能卡牌, w_4 +4 万能卡牌.

PockerGenerate.generateResourceName 函数可以根据PokerMeta生成对应的资源名

